import React, { useState, useRef, useEffect } from 'react';
import { SunIcon, MoonIcon, MonitorIcon, CheckIcon, UserIcon, FileTextIcon, LogOutIcon } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  isOpen: boolean;
  onClose: () => void;
  triggerRef: React.RefObject<HTMLElement>;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ isOpen, onClose, triggerRef }) => {
  const { theme, setTheme, resolvedTheme } = useTheme();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const themeOptions = [
    {
      value: 'light' as const,
      icon: <SunIcon className="w-4 h-4" />,
    },
    {
      value: 'dark' as const,
      icon: <MoonIcon className="w-4 h-4" />,
    },
    {
      value: 'system' as const,
      icon: <MonitorIcon className="w-4 h-4" />,
    }
  ];

  // Position dropdown relative to trigger
  const [position, setPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    if (isOpen && triggerRef.current && dropdownRef.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const dropdownRect = dropdownRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Calculate preferred position (above trigger, aligned to right)
      let top = triggerRect.top - dropdownRect.height - 8;
      let left = triggerRect.right - dropdownRect.width;

      // Check if dropdown would overflow above the viewport
      if (top < 8) {
        // Not enough space above, position below the trigger instead
        top = triggerRect.bottom + 8;

        // If still overflows below, position it to fit within viewport
        if (top + dropdownRect.height > viewportHeight - 8) {
          top = viewportHeight - dropdownRect.height - 8;
        }
      }

      // Check if dropdown would overflow to the left
      if (left < 8) {
        left = 8;
      }

      // Check if dropdown would overflow to the right
      if (left + dropdownRect.width > viewportWidth - 8) {
        left = viewportWidth - dropdownRect.width - 8;
      }

      setPosition({ top, left });
    }
  }, [isOpen, triggerRef]);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose, triggerRef]);

  // Close on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleThemeSelect = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    // Don't close immediately to match OpenAI behavior
  };

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-40" onClick={onClose} />
      
      {/* Dropdown */}
      <div
        ref={dropdownRef}
        className="fixed z-50 w-[250px] bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg shadow-lg animate-in fade-in slide-in-from-bottom-2 duration-200"
        style={{
          top: position.top,
          left: position.left,
        }}
      >
        {/* User Info Header */}
        <div className="p-4 border-b border-[var(--border-primary)]">
          <div className="text-[var(--text-primary)] font-semibold text-sm font-['Manrope',sans-serif]">
            John Smith
          </div>
          <div className="text-[var(--text-tertiary)] text-sm font-['Figtree',sans-serif]">
            <EMAIL>
          </div>
        </div>

        {/* Theme Toggle Section */}
        <div className="p-3 border-b border-[var(--border-primary)]">
          <div className="flex items-center justify-between">
            {themeOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleThemeSelect(option.value)}
                className={`p-2 rounded-md transition-colors ${
                  theme === option.value 
                    ? 'bg-[var(--bg-tertiary)] text-[var(--text-primary)]' 
                    : 'text-[var(--text-tertiary)] hover:bg-[var(--bg-tertiary)] hover:text-[var(--text-primary)]'
                }`}
                title={option.value === 'system' ? 'System' : option.value === 'light' ? 'Light' : 'Dark'}
              >
                {option.icon}
              </button>
            ))}
          </div>
        </div>

        {/* Menu Options */}
        <div className="py-1">
          <button className="w-full flex items-center gap-3 px-4 py-3 hover:bg-[var(--bg-tertiary)] transition-colors text-left">
            <UserIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            <span className="text-[var(--text-primary)] font-medium text-sm font-['Figtree',sans-serif]">
              Your profile
            </span>
          </button>
          
          <button className="w-full flex items-center gap-3 px-4 py-3 hover:bg-[var(--bg-tertiary)] transition-colors text-left">
            <FileTextIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            <span className="text-[var(--text-primary)] font-medium text-sm font-['Figtree',sans-serif]">
              Terms & policies
            </span>
          </button>
          
          <button className="w-full flex items-center gap-3 px-4 py-3 hover:bg-[var(--bg-tertiary)] transition-colors text-left">
            <LogOutIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
            <span className="text-[var(--text-primary)] font-medium text-sm font-['Figtree',sans-serif]">
              Log out
            </span>
          </button>
        </div>
      </div>
    </>
  );
};

export default ThemeToggle;