import React, { useState } from 'react';
import {
  ChevronRightIcon,
  ChevronLeftIcon,
  CheckIcon,
  GitBranchIcon,
  UsersIcon,
  SettingsIcon,
  BrainIcon,
  WrenchIcon,
  ClipboardCheckIcon,
  BarChart3Icon,
  RefreshCwIcon,
  AlertCircleIcon,
  ExternalLinkIcon,
  PlayIcon
} from 'lucide-react';
import { Card, CardContent } from '../../components/ui/card';
import { Link } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';

interface Step {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: 'pending' | 'in-progress' | 'completed' | 'skipped';
  content: React.ReactNode;
}

const OnboardingPage: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTeam, setSelectedTeam] = useState('');
  const [hasGitIntegration, setHasGitIntegration] = useState(false);

  // Mock teams data - in real app this would come from GitHub/GitLab
  const mockTeams = [
    'Web Frontend Team',
    'Web Backend Team', 
    'Mobile Team',
    'Data Engineering Team',
    'DevOps Team',
    'Platform Engineering Team'
  ];

  const steps: Step[] = [
    {
      id: 1,
      title: 'Pick a Team',
      description: 'Select a team to onboard or configure GitHub/GitLab integration',
      icon: <UsersIcon className="w-6 h-6" />,
      status: currentStep === 1 ? 'in-progress' : currentStep > 1 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Select Your Team</h3>
            
            {!hasGitIntegration ? (
              <div className="space-y-4">
                <div className="bg-amber-900/20 border border-amber-600/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircleIcon className="w-5 h-5 text-amber-400" />
                    <span className="text-amber-300 font-semibold">No Git Integration Found</span>
                  </div>
                  <p className="text-amber-200 text-sm mb-4">
                    AccelOS uses GitHub or GitLab as the source of truth for Teams. Please configure one of these integrations first.
                  </p>
                  <div className="flex gap-3">
                    <Link 
                      to="/settings/tool-integrations"
                      className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors"
                    >
                      <GitBranchIcon className="w-4 h-4" />
                      Configure GitHub/GitLab
                      <ExternalLinkIcon className="w-4 h-4" />
                    </Link>
                    <button 
                      onClick={() => setHasGitIntegration(true)}
                      className="px-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                    >
                      I've already configured it
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-900/20 border border-green-600/50 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckIcon className="w-5 h-5 text-green-400" />
                    <span className="text-green-300 font-semibold">Git Integration Active</span>
                  </div>
                  <p className="text-green-200 text-sm">
                    Teams have been synced from your Git repositories.
                  </p>
                </div>
                
                <div>
                  <label className="text-[var(--text-secondary)] text-sm block mb-2 font-medium">
                    Select a Team to Onboard
                  </label>
                  <select
                    value={selectedTeam}
                    onChange={(e) => setSelectedTeam(e.target.value)}
                    className="w-full bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg px-4 py-3 text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400"
                  >
                    <option value="">Choose a team...</option>
                    {mockTeams.map((team) => (
                      <option key={team} value={team}>{team}</option>
                    ))}
                  </select>
                </div>
                
                {selectedTeam && (
                  <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                    <h4 className="text-[var(--text-primary)] font-semibold mb-2">Team: {selectedTeam}</h4>
                    <div className="text-[var(--text-secondary)] text-sm space-y-1">
                      <p>• 12 repositories</p>
                      <p>• 8 active developers</p>
                      <p>• Primary languages: TypeScript, Python</p>
                      <p>• Main frameworks: React, Node.js, FastAPI</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      id: 2,
      title: 'Ensure Tool Integrations',
      description: 'Verify all tools used by your team are properly integrated',
      icon: <SettingsIcon className="w-6 h-6" />,
      status: currentStep === 2 ? 'in-progress' : currentStep > 2 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Tool Integration Status</h3>
            
            <div className="space-y-4">
              {[
                { name: 'GitHub', category: 'Code Repository', status: 'connected', required: true },
                { name: 'GitHub Actions', category: 'CI/CD Pipeline', status: 'connected', required: true },
                { name: 'Atlassian Jira', category: 'Task Management', status: 'connected', required: false },
                { name: 'SonarQube', category: 'Code Quality', status: 'not-connected', required: true },
                { name: 'Snyk', category: 'Security Testing', status: 'not-connected', required: true },
                { name: 'Datadog', category: 'Observability', status: 'connected', required: false },
              ].map((tool) => (
                <div key={tool.name} className="flex items-center justify-between p-4 bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      tool.status === 'connected' ? 'bg-green-400' : 'bg-red-400'
                    }`}></div>
                    <div>
                      <div className="text-[var(--text-primary)] font-medium">{tool.name}</div>
                      <div className="text-[var(--text-tertiary)] text-sm">{tool.category}</div>
                    </div>
                    {tool.required && (
                      <span className="px-2 py-1 bg-amber-900/20 text-amber-400 text-xs rounded-full border border-amber-600/50">
                        Required
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {tool.status === 'connected' ? (
                      <span className="text-green-400 text-sm font-medium">Connected</span>
                    ) : (
                      <Link 
                        to="/settings/tool-integrations"
                        className="px-3 py-1 bg-[#7678ed] hover:bg-[#6366f1] text-white text-sm rounded-lg transition-colors"
                      >
                        Connect
                      </Link>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 p-4 bg-cyan-900/20 border border-cyan-600/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckIcon className="w-5 h-5 text-cyan-400" />
                <span className="text-cyan-300 font-semibold">Integration Recommendations</span>
              </div>
              <p className="text-cyan-200 text-sm">
                Based on your team's repositories, we recommend connecting SonarQube and Snyk for comprehensive code quality and security scanning.
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 3,
      title: 'Build Engineering Knowledge Graph',
      description: 'Use the Knowledge Graph Agent to map your team\'s engineering ecosystem',
      icon: <BrainIcon className="w-6 h-6" />,
      status: currentStep === 3 ? 'in-progress' : currentStep > 3 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Engineering Knowledge Graph</h3>
            
            <div className="space-y-4">
              <p className="text-[var(--text-secondary)]">
                The Knowledge Graph Agent will analyze your team's repositories, dependencies, and infrastructure to create a comprehensive map of your engineering ecosystem.
              </p>
              
              <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                <h4 className="text-[var(--text-primary)] font-semibold mb-3">What will be mapped:</h4>
                <div className="grid grid-cols-2 gap-4 text-[var(--text-secondary)] text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      <span>Domains & Systems</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span>Components & Services</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span>Code Repositories</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                      <span>CI/CD Pipelines</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                      <span>Infrastructure</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                      <span>Dependencies</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Link 
                  to="/chat-sessions/new?agent=Knowledge%20Graph%20Agent"
                  className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors"
                >
                  <BrainIcon className="w-4 h-4" />
                  Start Knowledge Graph Agent
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
                <Link 
                  to="/knowledge-graph"
                  className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                >
                  View Existing Graph
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
              </div>
              
              <div className="bg-green-900/20 border border-green-600/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckIcon className="w-5 h-5 text-green-400" />
                  <span className="text-green-300 font-semibold">Estimated Time: 10-15 minutes</span>
                </div>
                <p className="text-green-200 text-sm">
                  The agent will automatically discover and map your team's engineering assets.
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 4,
      title: 'Configure Tools with AI Agents',
      description: 'Set up AI Coding Agents and other tools according to engineering standards',
      icon: <WrenchIcon className="w-6 h-6" />,
      status: currentStep === 4 ? 'in-progress' : currentStep > 4 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Tool Configuration</h3>
            
            <div className="space-y-4">
              <p className="text-[var(--text-secondary)]">
                Use the Tool Configuration Agent to set up your team's tools according to engineering standards. We recommend starting with AI Coding Agents.
              </p>
              
              <div className="bg-amber-900/20 border border-amber-600/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <WrenchIcon className="w-5 h-5 text-amber-400" />
                  <span className="text-amber-300 font-semibold">Recommended: Start with AI Coding Agents</span>
                </div>
                <p className="text-amber-200 text-sm">
                  Configure Cursor, GitHub Copilot, and other AI coding tools to follow your team's coding standards and best practices.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                  <h4 className="text-[var(--text-primary)] font-semibold mb-2">AI Coding Agents</h4>
                  <div className="space-y-2 text-[var(--text-secondary)] text-sm">
                    <div className="flex items-center gap-2">
                      <CheckIcon className="w-4 h-4 text-green-400" />
                      <span>Cursor (Connected)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckIcon className="w-4 h-4 text-green-400" />
                      <span>GitHub Copilot (Connected)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <AlertCircleIcon className="w-4 h-4 text-amber-400" />
                      <span>Configuration Needed</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                  <h4 className="text-[var(--text-primary)] font-semibold mb-2">Other Tools</h4>
                  <div className="space-y-2 text-[var(--text-secondary)] text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded border border-[var(--border-primary)]"></div>
                      <span>Code Review Agents</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded border border-[var(--border-primary)]"></div>
                      <span>Testing Tools</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded border border-[var(--border-primary)]"></div>
                      <span>Security Scanners</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Link 
                  to="/chat-sessions/new?agent=Tool%20Configuration%20Agent"
                  className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors"
                >
                  <WrenchIcon className="w-4 h-4" />
                  Start Tool Configuration Agent
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 5,
      title: 'Create Scorecards',
      description: 'Set up automated scorecards and shift-left quality checks',
      icon: <ClipboardCheckIcon className="w-6 h-6" />,
      status: currentStep === 5 ? 'in-progress' : currentStep > 5 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Scorecards</h3>
            
            <div className="space-y-4">
              <p className="text-[var(--text-secondary)]">
                Use the Scorecards Agent to create automated production readiness reviews and implement them as pre-commit checks in your repositories.
              </p>
              
              <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                <h4 className="text-[var(--text-primary)] font-semibold mb-3">Scorecard Categories:</h4>
                <div className="grid grid-cols-2 gap-4 text-[var(--text-secondary)] text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ClipboardCheckIcon className="w-4 h-4 text-green-400" />
                      <span>Code Quality</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ClipboardCheckIcon className="w-4 h-4 text-blue-400" />
                      <span>Security Compliance</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ClipboardCheckIcon className="w-4 h-4 text-purple-400" />
                      <span>Test Coverage</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ClipboardCheckIcon className="w-4 h-4 text-orange-400" />
                      <span>Performance</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ClipboardCheckIcon className="w-4 h-4 text-cyan-400" />
                      <span>Documentation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <ClipboardCheckIcon className="w-4 h-4 text-amber-400" />
                      <span>Deployment Readiness</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-cyan-900/20 border border-cyan-600/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <ClipboardCheckIcon className="w-5 h-5 text-cyan-400" />
                  <span className="text-cyan-300 font-semibold">Shift-Left Implementation</span>
                </div>
                <p className="text-cyan-200 text-sm">
                  Scorecards will be automatically integrated as pre-commit hooks and CI/CD checks to catch issues early in the development process.
                </p>
              </div>
              
              <div className="flex gap-3">
                <Link 
                  to="/chat-sessions/new?agent=Scorecards%20Agent"
                  className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors"
                >
                  <ClipboardCheckIcon className="w-4 h-4" />
                  Start Scorecards Agent
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
                <Link 
                  to="/scorecards"
                  className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                >
                  View Scorecards
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 6,
      title: 'Monitor Executions',
      description: 'Track and monitor the usage of your configured tools and agents',
      icon: <BarChart3Icon className="w-6 h-6" />,
      status: currentStep === 6 ? 'in-progress' : currentStep > 6 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Execution Monitoring</h3>
            
            <div className="space-y-4">
              <p className="text-[var(--text-secondary)]">
                Monitor the usage and performance of your configured tools and agents through the Executions dashboard.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-cyan-400 mb-1">24</div>
                  <div className="text-[var(--text-secondary)] text-sm">Tool Executions Today</div>
                </div>
                <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400 mb-1">98%</div>
                  <div className="text-[var(--text-secondary)] text-sm">Success Rate</div>
                </div>
                <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400 mb-1">156</div>
                  <div className="text-[var(--text-secondary)] text-sm">Total This Week</div>
                </div>
              </div>
              
              <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                <h4 className="text-[var(--text-primary)] font-semibold mb-3">What you can monitor:</h4>
                <div className="space-y-2 text-[var(--text-secondary)] text-sm">
                  <div className="flex items-center gap-2">
                    <BarChart3Icon className="w-4 h-4 text-cyan-400" />
                    <span>Tool Configuration Agent executions</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BarChart3Icon className="w-4 h-4 text-green-400" />
                    <span>Scorecards Agent runs and results</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BarChart3Icon className="w-4 h-4 text-purple-400" />
                    <span>AI Coding Agent usage patterns</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BarChart3Icon className="w-4 h-4 text-orange-400" />
                    <span>Pre-commit check results</span>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Link 
                  to="/executions"
                  className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors"
                >
                  <BarChart3Icon className="w-4 h-4" />
                  View Executions Dashboard
                  <ExternalLinkIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 7,
      title: 'Repeat for New Teams',
      description: 'Scale your setup by onboarding additional teams',
      icon: <RefreshCwIcon className="w-6 h-6" />,
      status: currentStep === 7 ? 'in-progress' : currentStep > 7 ? 'completed' : 'pending',
      content: (
        <div className="space-y-6">
          <div className="bg-[var(--bg-secondary)] border border-cyan-400/40 rounded-lg p-6">
            <h3 className="text-[var(--text-primary)] font-bold text-lg mb-4">Scale to Additional Teams</h3>
            
            <div className="space-y-4">
              <p className="text-[var(--text-secondary)]">
                Now that you've successfully onboarded your first team, you can repeat this process for additional teams in your organization.
              </p>
              
              <div className="bg-green-900/20 border border-green-600/50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckIcon className="w-5 h-5 text-green-400" />
                  <span className="text-green-300 font-semibold">Onboarding Complete!</span>
                </div>
                <p className="text-green-200 text-sm">
                  You've successfully set up {selectedTeam || 'your team'} with AccelOS. The team now has automated tools, quality checks, and monitoring in place.
                </p>
              </div>
              
              <div className="bg-[var(--bg-primary)] border border-[var(--border-primary)] rounded-lg p-4">
                <h4 className="text-[var(--text-primary)] font-semibold mb-3">Available Teams to Onboard:</h4>
                <div className="space-y-2">
                  {mockTeams.filter(team => team !== selectedTeam).map((team) => (
                    <div key={team} className="flex items-center justify-between p-3 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg">
                      <span className="text-[var(--text-secondary)]">{team}</span>
                      <button 
                        onClick={() => {
                          setSelectedTeam(team);
                          setCurrentStep(1);
                        }}
                        className="px-3 py-1 bg-[#7678ed] hover:bg-[#6366f1] text-white text-sm rounded-lg transition-colors"
                      >
                        Start Onboarding
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex gap-3">
                <button 
                  onClick={() => {
                    setSelectedTeam('');
                    setCurrentStep(1);
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors"
                >
                  <RefreshCwIcon className="w-4 h-4" />
                  Onboard Another Team
                </button>
                <Link 
                  to="/"
                  className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                >
                  Return to Dashboard
                </Link>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepId: number) => {
    setCurrentStep(stepId);
  };

  const currentStepData = steps.find(step => step.id === currentStep);

  return (
    <div className="min-h-screen bg-[var(--bg-primary)] text-[var(--text-primary)] p-8 font-['Manrope',sans-serif]">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="font-bold text-[var(--text-primary)] text-3xl mb-2">Automate a Team's Code to Customer Journey</h1>
        </div>

        <div className="flex gap-8">
          {/* Steps Sidebar */}
          <div className="w-80 flex-shrink-0">
            <Card className="bg-[var(--bg-secondary)] border border-cyan-400/40 sticky top-8">
              <CardContent className="p-6">
                <h2 className="font-bold text-[var(--text-primary)] text-lg mb-4">Steps</h2>
                <div className="space-y-3">
                  {steps.map((step, index) => (
                    <button
                      key={step.id}
                      onClick={() => goToStep(step.id)}
                      className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all ${
                        step.id === currentStep
                          ? 'bg-[#7678ed] text-white'
                          : step.status === 'completed'
                          ? resolvedTheme === 'light'
                            ? 'bg-green-600/80 text-white hover:bg-green-600/90'
                            : 'bg-green-900/20 text-green-300 hover:bg-green-900/30'
                          : 'bg-[var(--bg-primary)] text-[var(--text-secondary)] hover:bg-[var(--bg-tertiary)]'
                      }`}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        step.status === 'completed'
                          ? resolvedTheme === 'light'
                            ? 'bg-green-700 text-white'
                            : 'bg-green-500'
                          : step.id === currentStep
                          ? 'bg-white text-[#7678ed]'
                          : 'bg-[var(--border-secondary)]'
                      }`}>
                        {step.status === 'completed' ? (
                          <CheckIcon className="w-4 h-4" />
                        ) : (
                          <span className="text-sm font-bold">{step.id}</span>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-semibold text-sm truncate">{step.title}</div>
                        <div className="text-xs opacity-75 truncate">{step.description}</div>
                      </div>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <Card className="bg-[var(--bg-secondary)] border border-cyan-400/40 mb-6">
              <CardContent className="p-8">
                {/* Step Header */}
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 rounded-full bg-[#7678ed] flex items-center justify-center">
                    {currentStepData?.icon}
                  </div>
                  <div>
                    <h2 className="font-bold text-[var(--text-primary)] text-2xl">
                      Step {currentStep}: {currentStepData?.title}
                    </h2>
                    <p className="text-[var(--text-secondary)]">{currentStepData?.description}</p>
                  </div>
                </div>

                {/* Step Content */}
                <div className="mb-8">
                  {currentStepData?.content}
                </div>

                {/* Navigation */}
                <div className="flex items-center justify-between pt-6 border-t border-[var(--border-primary)]">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 1}
                    className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                    Previous
                  </button>

                  <div className="flex items-center gap-2">
                    <span className="text-[var(--text-tertiary)] text-sm">
                      {currentStep} of {steps.length}
                    </span>
                    <div className="flex gap-1">
                      {steps.map((_, index) => (
                        <div
                          key={index}
                          className={`w-2 h-2 rounded-full ${
                            index + 1 <= currentStep ? 'bg-[#7678ed]' : 'bg-[var(--border-secondary)]'
                          }`}
                        />
                      ))}
                    </div>
                  </div>

                  <button
                    onClick={nextStep}
                    disabled={currentStep === steps.length}
                    className="flex items-center gap-2 px-4 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {currentStep === steps.length ? 'Complete' : 'Next'}
                    <ChevronRightIcon className="w-4 h-4" />
                  </button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-[var(--bg-secondary)] border border-[var(--border-primary)]">
              <CardContent className="p-6">
                <h3 className="font-bold text-[var(--text-primary)] text-lg mb-4">Quick Actions</h3>
                <div className="flex gap-3">
                  <Link 
                    to="/settings/tool-integrations"
                    className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-primary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                  >
                    <SettingsIcon className="w-4 h-4" />
                    Tool Integrations
                  </Link>
                  <Link 
                    to="/agents"
                    className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-primary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                  >
                    <PlayIcon className="w-4 h-4" />
                    AI Agents
                  </Link>
                  <Link 
                    to="/executions"
                    className="flex items-center gap-2 px-4 py-2 bg-[var(--bg-primary)] border border-[var(--border-primary)] text-[var(--text-secondary)] rounded-lg hover:bg-[var(--bg-tertiary)] transition-colors"
                  >
                    <BarChart3Icon className="w-4 h-4" />
                    Executions
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;