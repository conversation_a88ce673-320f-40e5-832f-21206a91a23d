import { Card } from "../../components/ui/card";
import { agents } from "../Agents/index";
import { MessageSquareIcon, ServerIcon, SlackIcon, GithubIcon, ZapIcon, FilterIcon, XIcon } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";

const agentNames = agents.map(a => a.name);
const dummyExecutions = Array.from({ length: 10 }).map((_, idx) => {
  let status = ["active", "completed", "waiting-for-input"][idx % 3];
  return {
    id: `exec-${String(idx + 1).padStart(3, '0')}`,
    agent: agentNames[idx % agentNames.length],
    triggeredVia: ["Chat", "MCP Server", "Slack Webhook", "GitHub Webhook"][idx % 4],
    status,
    startTime: `2025-06-08 1${idx}:00:00`,
    endTime: status === "completed" || status === "error" ? `2025-06-08 1${idx}:20:00` : "",
  };
}).concat([
  {
    id: "exec-011",
    agent: agentNames[0],
    triggeredVia: "Chat",
    status: "error",
    startTime: "2025-06-08 10:00:00",
    endTime: "2025-06-08 10:20:00"
  },
  {
    id: "exec-012",
    agent: agentNames[1],
    triggeredVia: "MCP Server",
    status: "waiting-for-input",
    startTime: "2025-06-08 11:00:00",
    endTime: ""
  }
]);

const statusColors: Record<string, string> = {
  active: "bg-green-500/20 text-green-400 border-green-500/30",
  completed: "bg-blue-500/20 text-blue-400 border-blue-500/30",
  'waiting-for-input': "bg-yellow-500/20 text-yellow-400 border-yellow-500/30",
  error: "bg-red-500/20 text-red-400 border-red-500/30",
};

// Get unique values for filter options
const getUniqueAgents = () => [...new Set(dummyExecutions.map(exec => exec.agent))].sort();
const getUniqueTriggeredVia = () => [...new Set(dummyExecutions.map(exec => exec.triggeredVia))].sort();
const getUniqueStatuses = () => [...new Set(dummyExecutions.map(exec => exec.status))].sort();

export default function Executions() {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Filter states
  const [filters, setFilters] = useState({
    agent: '',
    triggeredVia: '',
    status: ''
  });
  
  const [filteredExecutions, setFilteredExecutions] = useState(dummyExecutions);

  // Initialize filters from URL params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const newFilters = {
      agent: params.get('agent') || '',
      triggeredVia: params.get('triggeredVia') || '',
      status: params.get('status') || ''
    };
    setFilters(newFilters);
  }, [location.search]);

  // Apply filters whenever filters change
  useEffect(() => {
    let filtered = dummyExecutions;

    if (filters.agent) {
      filtered = filtered.filter(exec => exec.agent === filters.agent);
    }
    if (filters.triggeredVia) {
      filtered = filtered.filter(exec => exec.triggeredVia === filters.triggeredVia);
    }
    if (filters.status) {
      filtered = filtered.filter(exec => exec.status === filters.status);
    }

    setFilteredExecutions(filtered);
  }, [filters]);

  // Update URL when filters change
  const updateFilters = (newFilters: typeof filters) => {
    setFilters(newFilters);
    
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });
    
    const newUrl = params.toString() ? `/executions?${params.toString()}` : '/executions';
    navigate(newUrl, { replace: true });
  };

  // Handle individual filter changes
  const handleFilterChange = (filterType: keyof typeof filters, value: string) => {
    updateFilters({
      ...filters,
      [filterType]: value
    });
  };

  // Clear individual filter
  const clearFilter = (filterType: keyof typeof filters) => {
    updateFilters({
      ...filters,
      [filterType]: ''
    });
  };

  // Clear all filters
  const clearAllFilters = () => {
    updateFilters({
      agent: '',
      triggeredVia: '',
      status: ''
    });
  };

  // Check if any filters are active
  const hasActiveFilters = Object.values(filters).some(filter => filter !== '');
  const activeFilterCount = Object.values(filters).filter(filter => filter !== '').length;

  // Helper function to format status display names
  const formatStatusName = (status: string) => {
    switch (status) {
      case 'waiting-for-input':
        return 'Waiting for Input';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <div className="p-6 font-['Manrope',sans-serif] bg-[var(--bg-primary)]">
      {/* Filter Controls */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <FilterIcon className="w-5 h-5 text-[var(--text-tertiary)]" />
            <span className="text-[var(--text-primary)] font-semibold">Filters</span>
            {hasActiveFilters && (
              <span className="px-2 py-1 bg-cyan-900/20 text-cyan-400 text-xs rounded-full border border-cyan-600/50">
                {activeFilterCount} active
              </span>
            )}
          </div>
          
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-cyan-400 hover:text-cyan-300 text-sm underline transition-colors"
            >
              Clear all filters
            </button>
          )}
        </div>

        {/* Filter Dropdowns */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Agent Filter */}
          <div>
            <label className="text-[var(--text-secondary)] text-sm block mb-2 font-medium">Agent Name</label>
            <div className="relative">
              <select
                value={filters.agent}
                onChange={(e) => handleFilterChange('agent', e.target.value)}
                className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-3 py-2 text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 appearance-none pr-8"
              >
                <option value="">All Agents</option>
                {getUniqueAgents().map((agent) => (
                  <option key={agent} value={agent}>{agent}</option>
                ))}
              </select>
              {filters.agent && (
                <button
                  onClick={() => clearFilter('agent')}
                  className="absolute right-8 top-1/2 transform -translate-y-1/2 text-[var(--text-tertiary)] hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>

          {/* Triggered Via Filter */}
          <div>
            <label className="text-[var(--text-secondary)] text-sm block mb-2 font-medium">Triggered Via</label>
            <div className="relative">
              <select
                value={filters.triggeredVia}
                onChange={(e) => handleFilterChange('triggeredVia', e.target.value)}
                className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-3 py-2 text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 appearance-none pr-8"
              >
                <option value="">All Triggers</option>
                {getUniqueTriggeredVia().map((trigger) => (
                  <option key={trigger} value={trigger}>{trigger}</option>
                ))}
              </select>
              {filters.triggeredVia && (
                <button
                  onClick={() => clearFilter('triggeredVia')}
                  className="absolute right-8 top-1/2 transform -translate-y-1/2 text-[var(--text-tertiary)] hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="text-[var(--text-secondary)] text-sm block mb-2 font-medium">Status</label>
            <div className="relative">
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg px-3 py-2 text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 appearance-none pr-8"
              >
                <option value="">All Statuses</option>
                {getUniqueStatuses().map((status) => (
                  <option key={status} value={status}>
                    {formatStatusName(status)}
                  </option>
                ))}
              </select>
              {filters.status && (
                <button
                  onClick={() => clearFilter('status')}
                  className="absolute right-8 top-1/2 transform -translate-y-1/2 text-[var(--text-tertiary)] hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="mt-4 flex flex-wrap items-center gap-2">
            <span className="text-[var(--text-secondary)] text-sm">Active filters:</span>
            {filters.agent && (
              <div className="flex items-center gap-1 px-3 py-1 bg-cyan-900/20 text-cyan-300 rounded-full border border-cyan-600/50">
                <span className="text-xs">Agent: {filters.agent}</span>
                <button
                  onClick={() => clearFilter('agent')}
                  className="text-cyan-400 hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-3 h-3" />
                </button>
              </div>
            )}
            {filters.triggeredVia && (
              <div className="flex items-center gap-1 px-3 py-1 bg-cyan-900/20 text-cyan-300 rounded-full border border-cyan-600/50">
                <span className="text-xs">Trigger: {filters.triggeredVia}</span>
                <button
                  onClick={() => clearFilter('triggeredVia')}
                  className="text-cyan-400 hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-3 h-3" />
                </button>
              </div>
            )}
            {filters.status && (
              <div className="flex items-center gap-1 px-3 py-1 bg-cyan-900/20 text-cyan-300 rounded-full border border-cyan-600/50">
                <span className="text-xs">
                  Status: {formatStatusName(filters.status)}
                </span>
                <button
                  onClick={() => clearFilter('status')}
                  className="text-cyan-400 hover:text-red-400 transition-colors"
                >
                  <XIcon className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className="mb-4 flex items-center justify-between">
        <div className="text-[var(--text-secondary)] text-sm">
          Showing {filteredExecutions.length} of {dummyExecutions.length} executions
          {hasActiveFilters && (
            <span className="text-cyan-400 ml-1">(filtered)</span>
          )}
        </div>
      </div>
      
      <Card className="overflow-x-auto bg-[var(--bg-secondary)] border border-cyan-400/40 shadow-lg">
        <table className="min-w-full divide-y divide-cyan-900 text-[var(--text-primary)]">
          <thead className="bg-[var(--bg-secondary)]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Execution ID</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Agent Name</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Triggered Via</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">Start Time</th>
              <th className="px-6 py-3 text-left text-xs font-bold text-cyan-300 uppercase tracking-wider">End Time</th>
            </tr>
          </thead>
          <tbody className="bg-[var(--bg-primary)] divide-y divide-[var(--border-primary)]">
            {filteredExecutions.length > 0 ? (
              filteredExecutions.map((exec) => (
                <tr key={exec.id} className="hover:bg-[var(--bg-tertiary)] transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[var(--text-primary)]">{exec.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[var(--text-primary)]">{exec.agent}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[var(--text-secondary)] flex items-center gap-2">
                    {exec.triggeredVia === "Chat" && <MessageSquareIcon className="w-4 h-4 text-cyan-300" />}
                    {exec.triggeredVia === "MCP Server" && <ServerIcon className="w-4 h-4 text-cyan-300" />}
                    {exec.triggeredVia === "Slack Webhook" && <SlackIcon className="w-4 h-4 text-cyan-300" />}
                    {exec.triggeredVia === "GitHub Webhook" && <GithubIcon className="w-4 h-4 text-cyan-300" />}
                    <span>{exec.triggeredVia}</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full border ${statusColors[exec.status]}`}>
                      {formatStatusName(exec.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[var(--text-secondary)]">{exec.startTime}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[var(--text-secondary)]">{(exec.status === 'completed' || exec.status === 'error') ? exec.endTime : ''}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="px-6 py-12 text-center">
                  <div className="text-[var(--text-tertiary)]">
                    <FilterIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">No executions found</h3>
                    <p className="text-sm">Try adjusting your filters to see more results.</p>
                    {hasActiveFilters && (
                      <button
                        onClick={clearAllFilters}
                        className="mt-3 text-cyan-400 hover:text-cyan-300 text-sm underline"
                      >
                        Clear all filters
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </Card>
    </div>
  );
}