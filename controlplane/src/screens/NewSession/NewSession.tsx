import {
  SendIcon,
  UserIcon,
  BotIcon,
  CopyIcon,
  ThumbsUpIcon,
  ThumbsDownIcon,
  RefreshCwIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  SearchIcon,
  PlusIcon,
  ChevronDownIcon,
} from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../../components/ui/avatar";
import { agents } from '../Agents/index';
import { Card, CardContent } from "../../components/ui/card";

interface Message {
  id?: number;
  role: "user" | "assistant";
  type: "user" | "assistant";
  content: string;
  timestamp?: string;
  avatar?: string | null;
  name?: string;
}

// Mocked knowledge graph nodes
const initialContextNodes = [
  { id: 1, name: "CI/CD Pipeline" },
  { id: 2, name: "React Application" },
  { id: 3, name: "Netlify Deployment" },
];

export const NewSession = (): JSX.Element => {
  const [messages, setMessages] = useState<Message[]>([
    {
      role: "assistant",
      type: "assistant",
      content: "Hello! I'm your Tool Configuration Agent. What would you like to do?",
      name: "AccelOS AI",
      avatar: null
    },
    {
      role: "user",
      type: "user",
      content: "I need to create Cursor.mdc files for our new microservices repository. Can you help me set up both organizational standards and team-specific standards?",
      name: "John Smith",
      avatar: "/profile.svg"
    },
    {
      role: "assistant",
      type: "assistant",
      content: "I'll help you create two Cursor.mdc files:\n\n1. `organization.mdc` - For company-wide coding standards\n2. `team.mdc` - For team-specific guidelines\n\nLet's start with the organizational standards. I'll create a template that includes:\n- Code style and formatting rules\n- Security best practices\n- Documentation requirements\n- Testing standards\n- Performance guidelines\n\nWould you like me to proceed with creating the organization.mdc file?",
      name: "AccelOS AI",
      avatar: null
    },
    {
      role: "user",
      type: "user",
      content: "Yes, please create the organization.mdc file first. Make sure to include our company's specific requirements for TypeScript and React development.",
      name: "John Smith",
      avatar: "/profile.svg"
    },
    {
      role: "assistant",
      type: "assistant",
      content: "I'll create the organization.mdc file with TypeScript and React-specific standards. Here's what I'm including:\n\n```markdown\n# AccelOS Organization Standards\n\n## TypeScript Standards\n- Strict type checking enabled\n- No any types without explicit justification\n- Interface-first approach for type definitions\n- Comprehensive JSDoc comments for public APIs\n\n## React Standards\n- Functional components with hooks\n- Proper prop typing with TypeScript\n- Memoization for expensive computations\n- Error boundaries for component isolation\n\n## Security\n- No sensitive data in client-side code\n- Input validation and sanitization\n- CSRF protection enabled\n- Regular dependency audits\n\n## Testing\n- Jest for unit testing\n- React Testing Library for component tests\n- Minimum 80% code coverage\n- E2E tests for critical paths\n\n## Performance\n- Code splitting by route\n- Lazy loading for heavy components\n- Image optimization\n- Bundle size monitoring\n```\n\nWould you like me to proceed with creating the team.mdc file next?",
      name: "AccelOS AI",
      avatar: null
    },
    {
      role: "user",
      type: "user",
      content: "Yes, please create the team.mdc file. Our team focuses on real-time data processing and analytics, so include specific guidelines for that.",
      name: "John Smith",
      avatar: "/profile.svg"
    },
    {
      role: "assistant",
      type: "assistant",
      content: "I'll create the team.mdc file with real-time data processing and analytics focus. Here's what I'm including:\n\n```markdown\n# Platform Engineering Team Standards\n\n## Real-time Data Processing\n- WebSocket connection management\n- Data stream optimization\n- Error handling and reconnection logic\n- State management with Redux Toolkit\n\n## Analytics Implementation\n- Consistent event tracking\n- Performance monitoring\n- Error tracking integration\n- Analytics dashboard standards\n\n## Team-specific Conventions\n- Feature branch naming: feature/RT-{ticket-number}\n- Commit message format: RT-{ticket-number}: {description}\n- PR template with performance impact section\n- Weekly performance review meetings\n\n## Documentation\n- API documentation with OpenAPI/Swagger\n- Architecture decision records (ADRs)\n- Performance benchmark documentation\n- Incident response playbooks\n```\n\nBoth files are now created in your repository. Would you like me to help you with anything else, such as setting up automated checks for these standards?",
      name: "AccelOS AI",
      avatar: null
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [contextNodes, setContextNodes] = useState(initialContextNodes);
  const [contextSearch, setContextSearch] = useState("");
  const [isContextOpen, setIsContextOpen] = useState(true);
  const [newNodeName, setNewNodeName] = useState("");
  const [selectedAgent, setSelectedAgent] = useState(agents[0]?.name || "");
  const [conversationStarted, setConversationStarted] = useState(false);

  // Set agent from query parameter on mount
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const agentParam = params.get('agent');
    if (agentParam && agents.some(a => a.name === agentParam)) {
      setSelectedAgent(agentParam);
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      // Mark conversation as started when user sends first message
      if (!conversationStarted) {
        setConversationStarted(true);
      }

      // Add user message
      setMessages((prev) => [
        ...prev,
        {
          id: prev.length + 1,
          role: "user",
          type: "user",
          content: inputValue,
          timestamp: new Date().toISOString(),
          avatar: "/profile.svg",
          name: "John Smith",
        },
      ]);
      setInputValue("");
      setIsTyping(true);

      // Simulate AI response after a delay
      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          {
            id: prev.length + 1,
            role: "assistant",
            type: "assistant",
            content: "I'm processing your request...",
            timestamp: new Date().toISOString(),
            avatar: null,
            name: "AccelOS AI",
          },
        ]);
        setIsTyping(false);
      }, 1000);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [inputValue]);

  // Filtered nodes for search
  const filteredNodes = contextNodes.filter((node) =>
    node.name.toLowerCase().includes(contextSearch.toLowerCase())
  );

  // Add new node
  const handleAddNode = () => {
    if (newNodeName.trim()) {
      setContextNodes((prev) => [
        ...prev,
        { id: prev.length + 1, name: newNodeName.trim() },
      ]);
      setNewNodeName("");
    }
  };

  return (
    <div className="flex h-full bg-[var(--bg-primary)] text-[var(--text-primary)]">
      {/* Chat Panel - NO GAP */}
      <div className="flex flex-col flex-1 h-full">
        {/* Agent Dropdown */}
        <div className="flex items-center px-4 pt-4 pb-2">
          <div className="relative w-full max-w-xs">
            {conversationStarted ? (
              // Display selected agent as uneditable when conversation has started
              <div className="bg-[var(--bg-secondary)] text-[var(--text-primary)] font-semibold rounded-md px-4 py-2 pr-10 border border-[var(--border-primary)] shadow-sm w-full opacity-60 cursor-not-allowed flex items-center justify-between">
                <span>{selectedAgent}</span>
                <span className="text-xs text-[var(--text-tertiary)] ml-2">(locked)</span>
              </div>
            ) : (
              // Editable dropdown when conversation hasn't started
              <>
                <select
                  value={selectedAgent}
                  onChange={e => setSelectedAgent(e.target.value)}
                  className="bg-[var(--bg-secondary)] text-[var(--text-primary)] font-semibold rounded-md px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-cyan-400/60 hover:border-cyan-300 appearance-none shadow-sm transition-all w-full"
                  style={{ minWidth: 180 }}
                >
                  {agents.map((agent) => (
                    <option key={agent.name} value={agent.name}>{agent.name}</option>
                  ))}
                </select>
                <ChevronDownIcon className="pointer-events-none absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
              </>
            )}
          </div>
        </div>
        {/* Messages */}
        <div className="flex-1 overflow-auto p-6 space-y-6">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-4 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.type === 'assistant' && (
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 rounded-full bg-[#7678ed] flex items-center justify-center">
                    <BotIcon className="w-5 h-5 text-white" />
                  </div>
                </div>
              )}
              
              <div className={`flex flex-col max-w-[70%] ${message.type === 'user' ? 'items-end' : 'items-start'}`}>
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-['Figtree',Helvetica] font-medium text-[var(--text-secondary)] text-sm">
                    {message.name}
                  </span>
                  <span className="font-['Figtree',Helvetica] font-normal text-[var(--text-tertiary)] text-xs">
                    {message.timestamp}
                  </span>
                </div>
                
                <div
                  className={`p-4 rounded-2xl ${
                    message.type === 'user'
                      ? 'bg-[#7678ed] text-white rounded-br-md'
                      : 'bg-[var(--bg-secondary)] border border-[var(--border-primary)] text-[var(--text-primary)] rounded-bl-md'
                  }`}
                >
                  <div className="font-['Figtree',Helvetica] font-normal text-sm leading-[19.6px] whitespace-pre-wrap">
                    {message.content}
                  </div>
                </div>
                
                {message.type === 'assistant' && (
                  <div className="flex items-center gap-2 mt-2">
                    <button className="p-1.5 hover:bg-[var(--bg-secondary)] rounded-lg transition-colors">
                      <CopyIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                    </button>
                    <button className="p-1.5 hover:bg-[var(--bg-secondary)] rounded-lg transition-colors">
                      <ThumbsUpIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                    </button>
                    <button className="p-1.5 hover:bg-[var(--bg-secondary)] rounded-lg transition-colors">
                      <ThumbsDownIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                    </button>
                    <button className="p-1.5 hover:bg-[var(--bg-secondary)] rounded-lg transition-colors">
                      <RefreshCwIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
                    </button>
                  </div>
                )}
              </div>

              {message.type === 'user' && (
                <div className="flex-shrink-0">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={message.avatar ?? undefined} alt={message.name} />
                    <AvatarFallback>
                      <UserIcon className="w-5 h-5" />
                    </AvatarFallback>
                  </Avatar>
                </div>
              )}
            </div>
          ))}

          {isTyping && (
            <div className="flex gap-4 justify-start">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 rounded-full bg-[#7678ed] flex items-center justify-center">
                  <BotIcon className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="flex flex-col max-w-[70%] items-start">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-['Figtree',Helvetica] font-medium text-[var(--text-secondary)] text-sm">
                    {selectedAgent}
                  </span>
                </div>
                <div className="p-4 rounded-2xl bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-bl-md">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-[var(--text-tertiary)] rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-[var(--text-tertiary)] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-[var(--text-tertiary)] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        {/* Input area styled to match app */}
        <div className="bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-lg m-4 mb-2">
          <div className="flex items-center px-4 py-2">
            <input
              type="text"
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter your message..."
              className="flex-1 bg-transparent text-[var(--text-primary)] border-none outline-none placeholder-[var(--text-tertiary)] text-base"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              className="ml-2 p-2 rounded-full bg-[#7678ed] hover:bg-[#6366f1] disabled:bg-[var(--text-tertiary)]/20 transition-colors flex items-center justify-center"
            >
              <SendIcon className="w-5 h-5 text-white" />
            </button>
          </div>
          <div className="flex items-center px-4 pb-2 text-xs text-[var(--text-tertiary)] justify-between">
            <span>{conversationStarted ? "Continue conversation" : "Enter to start session"}</span>
            <span className="font-semibold">Cmd + Enter</span>
            <span>{conversationStarted ? "to send message" : "to enter empty session"}</span>
          </div>
        </div>
      </div>
      {/* Collapsible Context Panel - NO GAP */}
      <div className={`relative transition-all duration-300 bg-[var(--bg-secondary)] h-full flex flex-col ${isContextOpen ? 'w-[340px] min-w-[300px]' : 'w-8 min-w-0'}`}>
        {/* Collapse/Expand Button */}
        <button
          className="absolute -left-4 top-4 z-20 bg-[var(--bg-secondary)] border border-[var(--border-primary)] rounded-full p-1 shadow hover:bg-[var(--bg-tertiary)] transition-colors"
          onClick={() => setIsContextOpen((open) => !open)}
          aria-label={isContextOpen ? 'Collapse context panel' : 'Expand context panel'}
        >
          {isContextOpen ? (
            <ChevronRightIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
          ) : (
            <ChevronLeftIcon className="w-4 h-4 text-[var(--text-tertiary)]" />
          )}
        </button>
        {isContextOpen && (
          <div className="flex flex-col h-full p-4">
            <h2 className="font-['Manrope',Helvetica] font-bold text-[var(--text-primary)] text-lg mb-4">Context for Grounding AI Agent</h2>
            {/* Search bar */}
            <div className="relative mb-4">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[var(--text-tertiary)]" />
              <input
                type="text"
                placeholder="Search EKG nodes..."
                value={contextSearch}
                onChange={(e) => setContextSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-[var(--bg-tertiary)] border border-[var(--border-secondary)] rounded-lg text-[var(--text-primary)] placeholder-[var(--text-tertiary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]/50 focus:border-[#7678ed]"
              />
            </div>
            {/* Node list */}
            <div className="flex-1 overflow-auto mb-4">
              {filteredNodes.length > 0 ? (
                <ul className="space-y-2">
                  {filteredNodes.map((node) => (
                    <li key={node.id} className="bg-[var(--border-secondary)] rounded-lg px-3 py-2 text-sm text-[var(--text-primary)]">
                      {node.name}
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="text-[var(--text-tertiary)] text-sm">No nodes found.</div>
              )}
            </div>
            {/* Add new node */}
            <div className="flex gap-2 mt-auto">
              <input
                type="text"
                placeholder="Add new node..."
                value={newNodeName}
                onChange={(e) => setNewNodeName(e.target.value)}
                className="flex-1 px-3 py-2 bg-[var(--bg-tertiary)] border border-[var(--border-secondary)] rounded-lg text-[var(--text-primary)] placeholder-[var(--text-tertiary)] focus:outline-none focus:ring-2 focus:ring-[#7678ed]/50 focus:border-[#7678ed]"
                onKeyDown={(e) => { if (e.key === 'Enter') handleAddNode(); }}
              />
              <button
                className="px-3 py-2 bg-[#7678ed] hover:bg-[#6366f1] text-white rounded-lg font-medium transition-colors"
                onClick={handleAddNode}
                aria-label="Add node"
              >
                <PlusIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};